# Handyman Admin Platform

一个为公司管理员和员工提供的高效后台系统，实现对 Handyman 任务的分配、管理、状态追踪、财务归档与数据可视化支持。

## 技术栈

- **前端**: React + Vite + TypeScript
- **后端**: Node.js + Express + TypeScript
- **数据库**: MySQL
- **认证**: JWT
- **端口**: 前端 8000, 后端 8001

## 功能特性

### 用户角色权限

| 角色 | 功能权限 |
|------|----------|
| 管理员 | 查看全部订单、分配任务、修改状态、上传/查看照片、确认结算、导出数据、查看日历 |
| 员工 | 查看被分配任务、上传施工照片、更新状态、提交结算请求 |
| 开发者 | 系统维护、权限修改、接口测试等 |

### 核心模块

1. **登录模块** - JWT认证系统
2. **任务总览Dashboard** - 支持筛选/搜索的任务概览
3. **任务详情页** - 完整的任务信息管理
4. **拖拽任务分配** - 直观的任务分配界面
5. **日历视图** - 日/周/月视图的任务展示
6. **财务与归档** - 费用管理和数据导出

## 快速开始

### 安装依赖
```bash
npm run install:all
```

### 开发环境启动
```bash
npm run dev
```

### 生产环境构建
```bash
npm run build
```

## 项目结构

```
handyman-admin-platform/
├── frontend/          # React前端应用
├── backend/           # Node.js后端API
├── database/          # 数据库脚本和配置
└── docs/             # 项目文档
```

## 环境配置

请参考各子项目中的 `.env.example` 文件配置相应的环境变量。
