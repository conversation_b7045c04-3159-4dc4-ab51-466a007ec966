# 🚀 Handyman Admin Platform - 快速启动指南

## 📋 前置要求

确保您的系统已安装以下软件：

- **Node.js** 18.0+ 
- **npm** 或 **yarn**
- **MySQL** 8.0+
- **Git**

## ⚡ 一键启动

### 1. 克隆项目
```bash
git clone <repository-url>
cd admin_platform
```

### 2. 自动设置
```bash
# 运行自动设置脚本
./setup.sh
```

### 3. 配置数据库
```bash
# 编辑后端环境变量
nano backend/.env

# 更新数据库连接字符串
DATABASE_URL="mysql://username:password@localhost:3306/handyman_db"
```

### 4. 初始化数据库
```bash
# 运行数据库初始化脚本
./init-db.sh
```

### 5. 启动应用
```bash
# 同时启动前端和后端
npm run dev
```

## 🌐 访问应用

- **前端界面**: http://localhost:8000
- **后端API**: http://localhost:8001

## 🔐 默认登录账户

| 角色 | 用户名 | 密码 | 邮箱 |
|------|--------|------|------|
| 管理员 | admin | admin123 | <EMAIL> |
| 员工 | john_smith | staff123 | <EMAIL> |
| 开发者 | developer | dev123 | <EMAIL> |

## 📱 功能导览

### 管理员功能
1. **Dashboard** - 查看系统概览和统计数据
2. **任务分配** - 拖拽分配任务给员工
3. **任务管理** - 创建、编辑、删除任务
4. **财务报表** - 查看收入、成本和利润分析
5. **日历视图** - 查看任务时间安排
6. **用户管理** - 管理员工账户

### 员工功能
1. **我的任务** - 查看分配给自己的任务
2. **状态更新** - 更新任务进度状态
3. **照片上传** - 上传施工前后照片
4. **时间记录** - 记录实际工作时间
5. **费用提交** - 提交任务相关费用

## 🛠️ 手动设置（可选）

如果自动脚本失败，可以手动执行以下步骤：

### 安装依赖
```bash
# 根目录
npm install

# 前端
cd frontend
npm install

# 后端
cd ../backend
npm install
```

### 环境配置
```bash
# 后端环境变量
cp backend/.env.example backend/.env

# 前端环境变量
cp frontend/.env.example frontend/.env
```

### 数据库设置
```bash
cd backend

# 生成Prisma客户端
npx prisma generate

# 运行数据库迁移
npx prisma migrate dev --name init

# 填充初始数据
npx prisma db seed
```

### 分别启动服务
```bash
# 终端1 - 启动后端
cd backend
npm run dev

# 终端2 - 启动前端
cd frontend
npm run dev
```

## 🔧 常见问题

### 数据库连接失败
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 启动MySQL服务
sudo systemctl start mysql

# 创建数据库
mysql -u root -p
CREATE DATABASE handyman_db;
```

### 端口冲突
如果端口8000或8001被占用：

```bash
# 查看端口占用
lsof -i :8000
lsof -i :8001

# 修改端口配置
# 前端: frontend/vite.config.ts
# 后端: backend/.env 中的 PORT
```

### 权限问题
```bash
# 给脚本执行权限
chmod +x setup.sh
chmod +x init-db.sh
```

## 📊 测试数据

系统已预置以下测试数据：

- **5个用户账户** (1个管理员，3个员工，1个开发者)
- **6个示例任务** (不同状态和优先级)
- **完整的任务状态流** (待分配→已分配→进行中→已完成)

## 🎯 下一步

1. **探索界面** - 使用不同角色账户登录体验功能
2. **创建任务** - 尝试创建新的维修任务
3. **分配任务** - 使用拖拽功能分配任务
4. **上传照片** - 测试照片上传功能
5. **查看报表** - 探索财务和统计功能

## 📞 技术支持

如果遇到问题：

1. 检查控制台错误信息
2. 查看服务器日志
3. 确认数据库连接
4. 验证环境变量配置

## 🎉 开始使用

现在您可以开始使用Handyman Admin Platform了！

访问 http://localhost:8000 并使用管理员账户登录开始探索系统功能。
