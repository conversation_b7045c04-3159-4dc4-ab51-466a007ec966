import { PrismaClient, UserRole, TaskStatus, Priority, PhotoType } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create default users
  const adminPassword = await bcrypt.hash('admin123', 12);
  const staffPassword = await bcrypt.hash('staff123', 12);
  const devPassword = await bcrypt.hash('dev123', 12);

  // Create admin user
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'admin',
      name: 'System Administrator',
      password: adminPassword,
      role: UserRole.ADMIN,
      isActive: true,
    },
  });

  // Create developer user
  const developer = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'developer',
      name: 'System Developer',
      password: devPassword,
      role: UserRole.DEVELOPER,
      isActive: true,
    },
  });

  // Create staff users
  const staff1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'john_smith',
      name: '<PERSON>',
      password: staffPassword,
      role: UserRole.STAFF,
      isActive: true,
    },
  });

  const staff2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'jane_doe',
      name: 'Jane Doe',
      password: staffPassword,
      role: UserRole.STAFF,
      isActive: true,
    },
  });

  const staff3 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'mike_wilson',
      name: 'Mike Wilson',
      password: staffPassword,
      role: UserRole.STAFF,
      isActive: true,
    },
  });

  console.log('✅ Users created');

  // Create sample tasks
  const sampleTasks = [
    {
      customerName: 'Alice Johnson',
      customerPhone: '******-0101',
      customerEmail: '<EMAIL>',
      serviceType: 'Plumbing',
      description: 'Fix leaking kitchen faucet and replace bathroom sink',
      address: '123 Main St, Anytown, ST 12345',
      status: TaskStatus.PENDING,
      priority: Priority.HIGH,
      estimatedHours: 3.0,
      deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      createdById: admin.id,
    },
    {
      customerName: 'Bob Smith',
      customerPhone: '******-0102',
      customerEmail: '<EMAIL>',
      serviceType: 'Electrical',
      description: 'Install ceiling fan in living room and fix flickering lights',
      address: '456 Oak Ave, Somewhere, ST 12346',
      status: TaskStatus.ASSIGNED,
      priority: Priority.MEDIUM,
      estimatedHours: 4.0,
      assignedToId: staff1.id,
      deadline: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
      createdById: admin.id,
    },
    {
      customerName: 'Carol Davis',
      customerPhone: '******-0103',
      customerEmail: '<EMAIL>',
      serviceType: 'Carpentry',
      description: 'Build custom shelving unit in home office',
      address: '789 Pine Rd, Elsewhere, ST 12347',
      status: TaskStatus.IN_PROGRESS,
      priority: Priority.LOW,
      estimatedHours: 8.0,
      actualHours: 4.5,
      assignedToId: staff2.id,
      deadline: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000), // 10 days from now
      createdById: admin.id,
    },
    {
      customerName: 'David Wilson',
      customerPhone: '******-0104',
      customerEmail: '<EMAIL>',
      serviceType: 'General Maintenance',
      description: 'Monthly maintenance check - HVAC, plumbing, electrical',
      address: '321 Elm St, Nowhere, ST 12348',
      status: TaskStatus.COMPLETED,
      priority: Priority.MEDIUM,
      estimatedHours: 2.0,
      actualHours: 2.5,
      cost: 150.00,
      revenue: 250.00,
      assignedToId: staff3.id,
      completedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      createdById: admin.id,
    },
    {
      customerName: 'Eva Brown',
      customerPhone: '******-0105',
      customerEmail: '<EMAIL>',
      serviceType: 'Painting',
      description: 'Paint master bedroom and hallway',
      address: '654 Maple Dr, Anywhere, ST 12349',
      status: TaskStatus.COMPLETED,
      priority: Priority.LOW,
      estimatedHours: 6.0,
      actualHours: 7.0,
      cost: 200.00,
      revenue: 400.00,
      assignedToId: staff1.id,
      completedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
      createdById: admin.id,
    },
    {
      customerName: 'Frank Miller',
      customerPhone: '******-0106',
      customerEmail: '<EMAIL>',
      serviceType: 'Appliance Repair',
      description: 'Repair washing machine - not draining properly',
      address: '987 Cedar Ln, Someplace, ST 12350',
      status: TaskStatus.ON_HOLD,
      priority: Priority.URGENT,
      estimatedHours: 2.0,
      assignedToId: staff2.id,
      deadline: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000), // 1 day from now
      createdById: admin.id,
    },
  ];

  for (const taskData of sampleTasks) {
    await prisma.task.create({
      data: taskData,
    });
  }

  console.log('✅ Sample tasks created');

  console.log('🎉 Database seeding completed!');
  console.log('\n📋 Default Users Created:');
  console.log('👤 Admin: <EMAIL> / admin123');
  console.log('👤 Developer: <EMAIL> / dev123');
  console.log('👤 Staff 1: <EMAIL> / staff123');
  console.log('👤 Staff 2: <EMAIL> / staff123');
  console.log('👤 Staff 3: <EMAIL> / staff123');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
