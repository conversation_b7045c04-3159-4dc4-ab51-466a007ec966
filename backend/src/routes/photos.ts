import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { body, validationResult } from 'express-validator';
import { PrismaClient, PhotoType } from '@prisma/client';
import { authenticateToken, AuthRequest, requireStaffOrAbove } from '../middleware/auth';

const router = express.Router();
const prisma = new PrismaClient();

// Ensure upload directory exists
const uploadDir = process.env.UPLOAD_DIR || 'uploads';
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  },
});

const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Check file type
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed!'));
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE || '5242880'), // 5MB default
  },
});

// Upload photos for a task
router.post('/upload/:taskId', [
  authenticateToken,
  requireStaffOrAbove,
  upload.array('photos', 10), // Allow up to 10 photos
  body('type').isIn(Object.values(PhotoType)).withMessage('Invalid photo type'),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array(),
      });
    }

    const taskId = parseInt(req.params.taskId);
    const { type } = req.body;
    const files = req.files as Express.Multer.File[];

    if (!files || files.length === 0) {
      return res.status(400).json({
        error: 'No files uploaded',
        code: 'NO_FILES',
      });
    }

    // Check if task exists and user has permission
    const task = await prisma.task.findUnique({
      where: { id: taskId },
    });

    if (!task) {
      // Clean up uploaded files
      files.forEach(file => {
        fs.unlinkSync(file.path);
      });
      
      return res.status(404).json({
        error: 'Task not found',
        code: 'TASK_NOT_FOUND',
      });
    }

    // Check permissions
    const canUpload = req.user!.role === 'ADMIN' || 
                     req.user!.role === 'DEVELOPER' ||
                     (req.user!.role === 'STAFF' && task.assignedToId === req.user!.id);

    if (!canUpload) {
      // Clean up uploaded files
      files.forEach(file => {
        fs.unlinkSync(file.path);
      });
      
      return res.status(403).json({
        error: 'Access denied',
        code: 'ACCESS_DENIED',
      });
    }

    // Save photo records to database
    const photoPromises = files.map(file => {
      const url = `/uploads/${file.filename}`;
      
      return prisma.photo.create({
        data: {
          taskId,
          type,
          filename: file.filename,
          originalName: file.originalname,
          mimetype: file.mimetype,
          size: file.size,
          url,
        },
      });
    });

    const photos = await Promise.all(photoPromises);

    res.status(201).json({
      message: 'Photos uploaded successfully',
      photos,
    });
  } catch (error) {
    // Clean up uploaded files on error
    if (req.files) {
      const files = req.files as Express.Multer.File[];
      files.forEach(file => {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      });
    }
    next(error);
  }
});

// Get photos for a task
router.get('/task/:taskId', [
  authenticateToken,
  requireStaffOrAbove,
], async (req: AuthRequest, res, next) => {
  try {
    const taskId = parseInt(req.params.taskId);

    // Check if task exists and user has permission
    const task = await prisma.task.findUnique({
      where: { id: taskId },
    });

    if (!task) {
      return res.status(404).json({
        error: 'Task not found',
        code: 'TASK_NOT_FOUND',
      });
    }

    // Check permissions
    const canView = req.user!.role === 'ADMIN' || 
                   req.user!.role === 'DEVELOPER' ||
                   (req.user!.role === 'STAFF' && task.assignedToId === req.user!.id);

    if (!canView) {
      return res.status(403).json({
        error: 'Access denied',
        code: 'ACCESS_DENIED',
      });
    }

    const photos = await prisma.photo.findMany({
      where: { taskId },
      orderBy: { uploadedAt: 'asc' },
    });

    res.json({ photos });
  } catch (error) {
    next(error);
  }
});

// Delete a photo
router.delete('/:id', [
  authenticateToken,
  requireStaffOrAbove,
], async (req: AuthRequest, res, next) => {
  try {
    const photoId = parseInt(req.params.id);

    const photo = await prisma.photo.findUnique({
      where: { id: photoId },
      include: {
        task: true,
      },
    });

    if (!photo) {
      return res.status(404).json({
        error: 'Photo not found',
        code: 'PHOTO_NOT_FOUND',
      });
    }

    // Check permissions
    const canDelete = req.user!.role === 'ADMIN' || 
                     req.user!.role === 'DEVELOPER' ||
                     (req.user!.role === 'STAFF' && photo.task.assignedToId === req.user!.id);

    if (!canDelete) {
      return res.status(403).json({
        error: 'Access denied',
        code: 'ACCESS_DENIED',
      });
    }

    // Delete file from filesystem
    const filePath = path.join(uploadDir, photo.filename);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // Delete record from database
    await prisma.photo.delete({
      where: { id: photoId },
    });

    res.json({ message: 'Photo deleted successfully' });
  } catch (error) {
    next(error);
  }
});

// Get single photo
router.get('/:id', [
  authenticateToken,
  requireStaffOrAbove,
], async (req: AuthRequest, res, next) => {
  try {
    const photoId = parseInt(req.params.id);

    const photo = await prisma.photo.findUnique({
      where: { id: photoId },
      include: {
        task: true,
      },
    });

    if (!photo) {
      return res.status(404).json({
        error: 'Photo not found',
        code: 'PHOTO_NOT_FOUND',
      });
    }

    // Check permissions
    const canView = req.user!.role === 'ADMIN' || 
                   req.user!.role === 'DEVELOPER' ||
                   (req.user!.role === 'STAFF' && photo.task.assignedToId === req.user!.id);

    if (!canView) {
      return res.status(403).json({
        error: 'Access denied',
        code: 'ACCESS_DENIED',
      });
    }

    res.json({ photo });
  } catch (error) {
    next(error);
  }
});

export default router;
