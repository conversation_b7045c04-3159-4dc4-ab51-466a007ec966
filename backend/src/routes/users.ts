import express from 'express';
import bcrypt from 'bcryptjs';
import { body, query, validationResult } from 'express-validator';
import { PrismaClient, UserRole } from '@prisma/client';
import { authenticateToken, AuthRequest, requireAdmin, requireAdminOrDeveloper } from '../middleware/auth';

const router = express.Router();
const prisma = new PrismaClient();

// Get all users (admin only)
router.get('/', [
  authenticateToken,
  requireAdminOrDeveloper,
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('role').optional().isIn(Object.values(UserRole)).withMessage('Invalid role'),
  query('isActive').optional().isBoolean().withMessage('isActive must be boolean'),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array(),
      });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const offset = (page - 1) * limit;

    // Build filter conditions
    const where: any = {};
    
    if (req.query.role) {
      where.role = req.query.role;
    }
    if (req.query.isActive !== undefined) {
      where.isActive = req.query.isActive === 'true';
    }
    if (req.query.search) {
      where.OR = [
        { name: { contains: req.query.search as string } },
        { username: { contains: req.query.search as string } },
        { email: { contains: req.query.search as string } },
      ];
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          username: true,
          email: true,
          name: true,
          role: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              assignedTasks: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
      }),
      prisma.user.count({ where }),
    ]);

    res.json({
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    next(error);
  }
});

// Get staff users for task assignment
router.get('/staff', [
  authenticateToken,
  requireAdmin,
], async (req: AuthRequest, res, next) => {
  try {
    const staff = await prisma.user.findMany({
      where: {
        role: 'STAFF',
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        username: true,
        email: true,
        _count: {
          select: {
            assignedTasks: {
              where: {
                status: {
                  in: ['ASSIGNED', 'IN_PROGRESS'],
                },
              },
            },
          },
        },
      },
      orderBy: { name: 'asc' },
    });

    res.json({ staff });
  } catch (error) {
    next(error);
  }
});

// Get single user
router.get('/:id', [
  authenticateToken,
  requireAdminOrDeveloper,
], async (req: AuthRequest, res, next) => {
  try {
    const userId = parseInt(req.params.id);
    
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        assignedTasks: {
          select: {
            id: true,
            customerName: true,
            status: true,
            priority: true,
            deadline: true,
            createdAt: true,
          },
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
        _count: {
          select: {
            assignedTasks: true,
            createdTasks: true,
          },
        },
      },
    });

    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        code: 'USER_NOT_FOUND',
      });
    }

    res.json({ user });
  } catch (error) {
    next(error);
  }
});

// Create new user
router.post('/', [
  authenticateToken,
  requireAdminOrDeveloper,
  body('username').isLength({ min: 3 }).withMessage('Username must be at least 3 characters'),
  body('email').isEmail().withMessage('Valid email is required'),
  body('name').notEmpty().withMessage('Name is required'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
  body('role').isIn(Object.values(UserRole)).withMessage('Invalid role'),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array(),
      });
    }

    const { username, email, name, password, role } = req.body;

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    const user = await prisma.user.create({
      data: {
        username,
        email,
        name,
        password: hashedPassword,
        role,
      },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        createdAt: true,
      },
    });

    res.status(201).json({
      message: 'User created successfully',
      user,
    });
  } catch (error) {
    next(error);
  }
});

// Update user
router.put('/:id', [
  authenticateToken,
  requireAdminOrDeveloper,
  body('username').optional().isLength({ min: 3 }).withMessage('Username must be at least 3 characters'),
  body('email').optional().isEmail().withMessage('Valid email is required'),
  body('name').optional().notEmpty().withMessage('Name cannot be empty'),
  body('role').optional().isIn(Object.values(UserRole)).withMessage('Invalid role'),
  body('isActive').optional().isBoolean().withMessage('isActive must be boolean'),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array(),
      });
    }

    const userId = parseInt(req.params.id);
    const updateData = { ...req.body };

    // Remove password from update data if present
    delete updateData.password;

    const user = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        updatedAt: true,
      },
    });

    res.json({
      message: 'User updated successfully',
      user,
    });
  } catch (error) {
    next(error);
  }
});

// Reset user password
router.put('/:id/reset-password', [
  authenticateToken,
  requireAdminOrDeveloper,
  body('newPassword').isLength({ min: 6 }).withMessage('New password must be at least 6 characters'),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array(),
      });
    }

    const userId = parseInt(req.params.id);
    const { newPassword } = req.body;

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    await prisma.user.update({
      where: { id: userId },
      data: { password: hashedPassword },
    });

    res.json({ message: 'Password reset successfully' });
  } catch (error) {
    next(error);
  }
});

// Delete user (soft delete by setting isActive to false)
router.delete('/:id', [
  authenticateToken,
  requireAdminOrDeveloper,
], async (req: AuthRequest, res, next) => {
  try {
    const userId = parseInt(req.params.id);

    // Check if user has active tasks
    const activeTasks = await prisma.task.count({
      where: {
        assignedToId: userId,
        status: {
          in: ['ASSIGNED', 'IN_PROGRESS'],
        },
      },
    });

    if (activeTasks > 0) {
      return res.status(400).json({
        error: 'Cannot delete user with active tasks',
        code: 'USER_HAS_ACTIVE_TASKS',
        activeTasks,
      });
    }

    await prisma.user.update({
      where: { id: userId },
      data: { isActive: false },
    });

    res.json({ message: 'User deactivated successfully' });
  } catch (error) {
    next(error);
  }
});

export default router;
