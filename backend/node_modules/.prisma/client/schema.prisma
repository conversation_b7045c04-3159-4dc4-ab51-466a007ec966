// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  username  String   @unique
  name      String
  password  String
  role      UserRole @default(STAFF)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  assignedTasks Task[] @relation("AssignedTasks")
  createdTasks  Task[] @relation("CreatedTasks")

  @@map("users")
}

model Task {
  id             Int        @id @default(autoincrement())
  customerName   String
  customerPhone  String?
  customerEmail  String?
  serviceType    String
  description    String     @db.Text
  address        String
  status         TaskStatus @default(PENDING)
  priority       Priority   @default(MEDIUM)
  estimatedHours Float?
  actualHours    Float?
  cost           Float?
  revenue        Float?
  deadline       DateTime?
  scheduledAt    DateTime?
  completedAt    DateTime?
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt

  // Relations
  assignedToId Int?
  assignedTo   User?   @relation("AssignedTasks", fields: [assignedToId], references: [id])
  createdById  Int
  createdBy    User    @relation("CreatedTasks", fields: [createdById], references: [id])
  photos       Photo[]

  @@map("tasks")
}

model Photo {
  id           Int       @id @default(autoincrement())
  taskId       Int
  type         PhotoType
  filename     String
  originalName String
  mimetype     String
  size         Int
  url          String
  uploadedAt   DateTime  @default(now())

  // Relations
  task Task @relation(fields: [taskId], references: [id], onDelete: Cascade)

  @@map("photos")
}

enum UserRole {
  ADMIN
  STAFF
  DEVELOPER
}

enum TaskStatus {
  PENDING // 待分配
  ASSIGNED // 已分配
  IN_PROGRESS // 进行中
  COMPLETED // 已完成
  CANCELLED // 已取消
  ON_HOLD // 暂停
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum PhotoType {
  BEFORE
  AFTER
  PROGRESS
}
