import {
  ClipboardList,
  Clock,
  CheckCircle,
  DollarSign,
  TrendingUp,
  Users,
  AlertCircle,
  Plus
} from 'lucide-react';

const Dashboard = () => {
  const { user } = useAuthStore();

  // Fetch task statistics
  const { data: statsData, isLoading: statsLoading } = useQuery({
    queryKey: ['task-stats'],
    queryFn: () => taskService.getTaskStats(),
  });

  // Fetch recent tasks
  const { data: tasksData, isLoading: tasksLoading } = useQuery({
    queryKey: ['recent-tasks'],
    queryFn: () => taskService.getTasks({ limit: 10, page: 1 }),
  });

  // Fetch staff count (admin only)
  const { data: staffData } = useQuery({
    queryKey: ['staff-count'],
    queryFn: () => userService.getStaffUsers(),
    enabled: user?.role === 'ADMIN',
  });

  const stats = statsData?.stats;
  const recentTasks = tasksData?.data || [];

  if (statsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome section */}
      <div className="bg-white rounded-lg shadow-soft p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Welcome back, {user?.name}!
            </h1>
            <p className="text-gray-600 mt-1">
              Here's what's happening with your tasks today.
            </p>
          </div>
          {user?.role === 'ADMIN' && (
            <button className="btn-primary btn-md">
              <Plus className="w-4 h-4 mr-2" />
              New Task
            </button>
          )}
        </div>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Tasks"
          value={stats?.totalTasks || 0}
          icon={ClipboardList}
          color="blue"
        />
        <StatsCard
          title="In Progress"
          value={stats?.inProgressTasks || 0}
          icon={Clock}
          color="yellow"
        />
        <StatsCard
          title="Completed"
          value={stats?.completedTasks || 0}
          icon={CheckCircle}
          color="green"
        />
        <StatsCard
          title="Revenue"
          value={formatCurrency(stats?.totalRevenue || 0)}
          icon={DollarSign}
          color="purple"
        />
      </div>

      {/* Additional stats for admin */}
      {user?.role === 'ADMIN' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <StatsCard
            title="Pending Tasks"
            value={stats?.pendingTasks || 0}
            icon={AlertCircle}
            color="red"
          />
          <StatsCard
            title="Active Staff"
            value={staffData?.staff?.length || 0}
            icon={Users}
            color="indigo"
          />
          <StatsCard
            title="Profit"
            value={formatCurrency(stats?.profit || 0)}
            icon={TrendingUp}
            color="green"
          />
        </div>
      )}

      {/* Main content grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent tasks */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-soft">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Recent Tasks</h2>
            </div>
            <div className="p-6">
              {tasksLoading ? (
                <div className="flex items-center justify-center h-32">
                  <LoadingSpinner />
                </div>
              ) : (
                <TaskList tasks={recentTasks} />
              )}
            </div>
          </div>
        </div>

        {/* Recent activity */}
        <div>
          <div className="bg-white rounded-lg shadow-soft">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
            </div>
            <div className="p-6">
              <RecentActivity tasks={recentTasks} />
            </div>
          </div>
        </div>
      </div>

      {/* Quick actions for different roles */}
      <div className="bg-white rounded-lg shadow-soft p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {user?.role === 'ADMIN' && (
            <>
              <button className="btn-outline btn-md justify-start">
                <Plus className="w-4 h-4 mr-2" />
                Create New Task
              </button>
              <button className="btn-outline btn-md justify-start">
                <Users className="w-4 h-4 mr-2" />
                Manage Staff
              </button>
              <button className="btn-outline btn-md justify-start">
                <ClipboardList className="w-4 h-4 mr-2" />
                Assign Tasks
              </button>
            </>
          )}
          {user?.role === 'STAFF' && (
            <>
              <button className="btn-outline btn-md justify-start">
                <Clock className="w-4 h-4 mr-2" />
                Update Task Status
              </button>
              <button className="btn-outline btn-md justify-start">
                <CheckCircle className="w-4 h-4 mr-2" />
                Complete Task
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
