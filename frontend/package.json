{"name": "handyman-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --port 8000", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "zustand": "^4.4.7", "@tanstack/react-query": "^5.8.4", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.1.0", "date-fns": "^2.30.0", "react-beautiful-dnd": "^13.1.1", "react-big-calendar": "^1.8.5", "recharts": "^2.8.0", "react-dropzone": "^14.2.3"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-beautiful-dnd": "^13.1.6", "@types/react-big-calendar": "^1.8.9", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.0"}}