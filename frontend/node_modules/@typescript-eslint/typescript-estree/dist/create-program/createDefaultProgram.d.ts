import type { ParseSettings } from '../parseSettings';
import type { ASTAndDefiniteProgram } from './shared';
/**
 * @param parseSettings Internal settings for parsing the file
 * @returns If found, returns the source file corresponding to the code and the containing program
 * @deprecated
 * This is a legacy option that comes with severe performance penalties.
 * Please do not use it.
 */
declare function createDefaultProgram(parseSettings: ParseSettings): ASTAndDefiniteProgram | undefined;
export { createDefaultProgram };
//# sourceMappingURL=createDefaultProgram.d.ts.map