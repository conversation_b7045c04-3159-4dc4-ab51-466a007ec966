{"version": 3, "file": "getContextualType.js", "sourceRoot": "", "sources": ["../src/getContextualType.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC;;;;GAIG;AACH,SAAgB,iBAAiB,CAC/B,OAAuB,EACvB,IAAmB;IAEnB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAE3B,IAAI,EAAE,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9D,IAAI,IAAI,KAAK,MAAM,CAAC,UAAU,EAAE,CAAC;YAC/B,2CAA2C;YAC3C,OAAO;QACT,CAAC;IACH,CAAC;SAAM,IACL,EAAE,CAAC,qBAAqB,CAAC,MAAM,CAAC;QAChC,EAAE,CAAC,qBAAqB,CAAC,MAAM,CAAC;QAChC,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,EACtB,CAAC;QACD,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAC5E,CAAC;SAAM,IAAI,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;QACtC,OAAO,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;IAC3C,CAAC;SAAM,IAAI,EAAE,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;QACpE,OAAO,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;SAAM,IACL,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC;QAC7B,MAAM,CAAC,aAAa,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW;QACvD,MAAM,CAAC,KAAK,KAAK,IAAI,EACrB,CAAC;QACD,uBAAuB;QACvB,OAAO,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;SAAM,IACL,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,YAAY,EAAE,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,QAAQ,CACjE,MAAM,CAAC,IAAI,CACZ,EACD,CAAC;QACD,oEAAoE;QACpE,OAAO;IACT,CAAC;IACD,2CAA2C;IAE3C,OAAO,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACzC,CAAC;AAvCD,8CAuCC"}