{"authors": "<PERSON> <https://github.com/Lakerfield>, <PERSON> <https://github.com/kingdango>, <PERSON><PERSON><PERSON> <https://github.com/horiuchi>, <PERSON> <https://github.com/DickvdBrink>, <PERSON><PERSON> <https://github.com/adidahiya>, <PERSON> <https://github.com/EnableSoftware>, <PERSON><PERSON> <https://github.com/galtalmor>", "definitionFilename": "index.d.ts", "libraryDependencies": [], "moduleDependencies": [], "libraryMajorVersion": "2", "libraryMinorVersion": "11", "libraryName": "Moment.js 2.11.1", "typingsPackageName": "moment", "projectName": "https://github.com/timrwood/moment", "sourceRepoURL": "https://www.github.com/DefinitelyTyped/DefinitelyTyped", "sourceBranch": "types-2.0", "kind": "UMD", "globals": ["moment"], "declaredModules": [], "files": ["index.d.ts"], "hasPackageJson": false, "contentHash": "cf58db647414bd10282c45942df05ace072cd32fe5288813e1593c1e956c9a02"}