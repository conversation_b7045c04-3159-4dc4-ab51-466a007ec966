# Installation
> `npm install --save @types/react-beautiful-dnd`

# Summary
This package contains type definitions for react-beautiful-dnd (https://github.com/atlassian/react-beautiful-dnd).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-beautiful-dnd.

### Additional Details
 * Last updated: Thu, 28 Dec 2023 22:06:53 GMT
 * Dependencies: [@types/react](https://npmjs.com/package/@types/react)

# Credits
These definitions were written by [var<PERSON><PERSON><PERSON>](https://github.com/varHarrie), [<PERSON>](https://github.com/bradleyayers), [<PERSON>](https://github.com/paustint), [<PERSON>](https://github.com/marknelissen), [<PERSON>](https://github.com/enricoboccadifuoco), [<PERSON><PERSON><PERSON>](https://github.com/lonyele), [<PERSON><PERSON><PERSON><PERSON><PERSON> Suja<PERSON>](https://github.com/lukyth), [<PERSON><PERSON>](https://github.com/aruniverse), [<PERSON>](https://github.com/nickgar<PERSON>), [<PERSON>](https://github.com/brianspowers), and [Declan Warn](https://github.com/declan-warn).
